const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/LMS', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
}).then(async () => {
  console.log('✅ Connected to LMS database');
  
  try {
    // Drop existing pdfs collection if it exists
    try {
      await mongoose.connection.db.collection('pdfs').drop();
      console.log('🗑️ Dropped existing pdfs collection');
    } catch (err) {
      console.log('ℹ️ No existing pdfs collection to drop');
    }

    // Create pdfs collection explicitly
    await mongoose.connection.db.createCollection('pdfs');
    console.log('✅ Created pdfs collection');

    // Find admin user
    const usersCollection = mongoose.connection.db.collection('users');
    let admin = await usersCollection.findOne({ email: '<EMAIL>' });
    
    if (!admin) {
      console.log('❌ Admin user not found, creating one...');
      const bcrypt = require('bcryptjs');
      const hashedPassword = await bcrypt.hash('admin123', 10);
      
      const adminUser = {
        name: 'Admin',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'admin',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      const result = await usersCollection.insertOne(adminUser);
      admin = { _id: result.insertedId, ...adminUser };
      console.log('✅ Admin user created');
    }

    // Create working PDF content
    const createPDFContent = (title, description) => {
      return `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
/Font <<
/F1 5 0 R
>>
>>
>>
endobj

4 0 obj
<<
/Length 200
>>
stream
BT
/F1 16 Tf
50 750 Td
(${title}) Tj
0 -30 Td
/F1 12 Tf
(${description}) Tj
0 -20 Td
(This is a working PDF document.) Tj
0 -20 Td
(Created for LMS system testing.) Tj
ET
endstream
endobj

5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj

xref
0 6
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000274 00000 n 
0000000525 00000 n 
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
620
%%EOF`;
    };

    // Create uploads directory if it doesn't exist
    const uploadsDir = path.join(__dirname, 'uploads/pdfs');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
      console.log('📁 Created uploads directory');
    }

    // Clear existing PDF files
    const existingFiles = fs.readdirSync(uploadsDir).filter(file => file.endsWith('.pdf'));
    existingFiles.forEach(file => {
      fs.unlinkSync(path.join(uploadsDir, file));
    });
    console.log('🗑️ Cleared existing PDF files');

    // PDF data to create
    const pdfData = [
      {
        title: 'Introduction to Computer Science',
        description: 'Basic concepts of computer science including programming fundamentals, data structures, and algorithms.',
        category: 'Academic',
        subject: 'Computer Science',
        tags: ['programming', 'computer science', 'fundamentals']
      },
      {
        title: 'Database Systems Guide',
        description: 'Comprehensive guide to database management systems, SQL, and database design principles.',
        category: 'Study Material',
        subject: 'Database Systems',
        tags: ['database', 'sql', 'dbms']
      },
      {
        title: 'Web Development Basics',
        description: 'Learn HTML, CSS, JavaScript and modern web development techniques.',
        category: 'Tutorial',
        subject: 'Web Development',
        tags: ['html', 'css', 'javascript', 'web']
      }
    ];

    // Get pdfs collection
    const pdfsCollection = mongoose.connection.db.collection('pdfs');

    // Create PDF files and database records
    for (let i = 0; i < pdfData.length; i++) {
      const data = pdfData[i];
      const timestamp = Date.now();
      const fileName = `pdf-${timestamp}-${i}.pdf`;
      const filePath = path.join(uploadsDir, fileName);
      
      // Create PDF file
      const pdfContent = createPDFContent(data.title, data.description);
      fs.writeFileSync(filePath, pdfContent);
      
      // Create database record
      const pdfRecord = {
        title: data.title,
        description: data.description,
        category: data.category,
        subject: data.subject,
        pdfUrl: `/uploads/pdfs/${fileName}`,
        fileName: fileName,
        originalName: `${data.title.toLowerCase().replace(/\s+/g, '-')}.pdf`,
        fileSize: (pdfContent.length / 1024).toFixed(2) + ' KB',
        fileSizeBytes: pdfContent.length,
        mimeType: 'application/pdf',
        pages: 1,
        tags: data.tags,
        downloadCount: 0,
        viewCount: 0,
        isActive: true,
        uploadedBy: admin._id,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      await pdfsCollection.insertOne(pdfRecord);
      console.log(`✅ Created PDF: ${data.title} (${fileName})`);
    }

    // Verify the collection has data
    const count = await pdfsCollection.countDocuments();
    console.log(`📊 PDFs collection now has ${count} documents`);

    // List all collections to confirm
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('\n📋 All collections in LMS database:');
    collections.forEach(collection => {
      console.log(`   - ${collection.name}`);
    });

    console.log('\n🎉 PDFs table created successfully!');
    console.log('\n🔗 Test URLs:');
    console.log('   📄 ExploreMore: http://localhost:3001/LearnX/explore-more');
    console.log('   🔧 Admin Panel: http://localhost:3001/LearnX/admin');
    console.log('   📊 PDF Status: http://localhost:5002/api/pdf-status');
    console.log('\n💡 Refresh MongoDB Compass to see the pdfs collection');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}).catch(err => {
  console.error('❌ MongoDB connection error:', err);
  process.exit(1);
});
